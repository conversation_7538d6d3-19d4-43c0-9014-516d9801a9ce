package com.hys.hm.shared.framework.controller;

import com.hys.hm.shared.common.Result;
import com.hys.hm.shared.common.page.PageRequest;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.common.query.QueryCondition;
import com.hys.hm.shared.common.query.QueryConditionParser;
import com.hys.hm.shared.common.query.SortOrder;
import com.hys.hm.shared.framework.service.BaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 基础控制器抽象类
 * 提供通用的REST API操作
 *
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@Slf4j
@Validated

@RequiredArgsConstructor
public abstract class BaseController<T, ID extends Serializable> {


    protected final BaseService<T, ID> baseService;


    protected final QueryConditionParser queryConditionParser;

    /**
     * 创建实体
     *
     * @param entity 实体对象
     * @return 创建结果
     */
    @PostMapping
    @Operation(summary = "创建实体", description = "创建新的实体记录")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @ResponseBody
    public Result<T> create(@Valid @RequestBody T entity) {
        try {
            log.info("创建实体: {}", entity);

            T savedEntity = baseService.save(entity);

            log.info("创建实体成功: ID={}", getEntityId(savedEntity));
            return Result.success("创建成功", savedEntity);

        } catch (IllegalArgumentException e) {
            log.warn("创建实体失败: {}", e.getMessage());
            return Result.error(Result.PARAM_ERROR_CODE, e.getMessage());
        } catch (Exception e) {
            log.error("创建实体异常: {}", e.getMessage(), e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建实体
     *
     * @param entities 实体列表
     * @return 创建结果
     */
    @PostMapping("/batch")
    @Operation(summary = "批量创建实体", description = "批量创建多个实体记录")
    @ResponseBody
    public Result<List<T>> createBatch(@Valid @RequestBody @NotEmpty List<T> entities) {
        try {
            log.info("批量创建实体: {} 条", entities.size());

            List<T> savedEntities = baseService.saveAll(entities);

            log.info("批量创建实体成功: {} 条", savedEntities.size());
            return Result.success("批量创建成功", savedEntities);

        } catch (IllegalArgumentException e) {
            log.warn("批量创建实体失败: {}", e.getMessage());
            return Result.error(Result.PARAM_ERROR_CODE, e.getMessage());
        } catch (Exception e) {
            log.error("批量创建实体异常: {}", e.getMessage(), e);
            return Result.error("批量创建失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询实体
     *
     * @param id 实体ID
     * @return 查询结果
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询实体", description = "根据实体ID查询单个实体记录")
    @ResponseBody
    public Result<T> getById(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull ID id) {
        try {
            log.debug("根据ID查询实体: ID={}", id);

            Optional<T> entityOpt = baseService.findById(id);
            return entityOpt.map(t -> Result.success("查询成功", t)).orElseGet(() -> Result.error(Result.NOT_FOUND_CODE, "实体不存在: ID=" + id));

        } catch (Exception e) {
            log.error("根据ID查询实体异常: ID={}, 错误={}", id, e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新实体
     *
     * @param id 实体ID
     * @param entity 实体对象
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新实体", description = "根据ID更新实体记录")
    @ResponseBody
    public Result<T> update(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull ID id,
            @Valid @RequestBody T entity) {
        try {
            log.info("更新实体: ID={}, 实体={}", id, entity);

            // 检查实体是否存在
            if (!baseService.existsById(id)) {
                return Result.error(Result.NOT_FOUND_CODE, "实体不存在: ID=" + id);
            }

            // 设置实体ID（确保ID一致）
            setEntityId(entity, id);

            T updatedEntity = baseService.update(entity);

            log.info("更新实体成功: ID={}", id);
            return Result.success("更新成功", updatedEntity);

        } catch (IllegalArgumentException e) {
            log.warn("更新实体失败: ID={}, 错误={}", id, e.getMessage());
            return Result.error(Result.PARAM_ERROR_CODE, e.getMessage());
        } catch (Exception e) {
            log.error("更新实体异常: ID={}, 错误={}", id, e.getMessage(), e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID删除实体
     *
     * @param id 实体ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除实体", description = "根据ID删除实体记录")
    @ResponseBody
    public Result<Boolean> deleteById(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull ID id) {
        try {
            log.info("删除实体: ID={}", id);

            boolean deleted = baseService.deleteById(id);
            if (deleted) {
                log.info("删除实体成功: ID={}", id);
                return Result.success(Boolean.TRUE);
            } else {
                log.warn("删除实体失败: ID={}", id);
                return Result.error(Result.NOT_FOUND_CODE, "实体不存在: ID=" + id);
            }

        } catch (Exception e) {
            log.error("删除实体异常: ID={}, 错误={}", id, e.getMessage(), e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除实体
     *
     * @param ids 实体ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除实体", description = "根据ID列表批量删除实体记录")
    @ResponseBody
    public Result<Integer> deleteBatch(@RequestBody @NotEmpty List<ID> ids) {
        try {
            log.info("批量删除实体: {} 条", ids.size());

            int deletedCount = baseService.deleteByIds(ids);

            log.info("批量删除实体成功: {} 条", deletedCount);
            return Result.success("批量删除成功", deletedCount);

        } catch (IllegalArgumentException e) {
            log.warn("批量删除实体失败: {}", e.getMessage());
            return Result.error(Result.PARAM_ERROR_CODE, e.getMessage());
        } catch (Exception e) {
            log.error("批量删除实体异常: {}", e.getMessage(), e);
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询实体列表
     *
     * @param request HTTP请求对象（用于获取查询参数）
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param orders 排序字符串（格式：+field1,-field2）
     * @return 分页查询结果
     */
    @GetMapping
    @Operation(summary = "分页查询实体列表", description = "根据查询条件分页查询实体列表")
    @ResponseBody
    public Result<PageResult<T>> findPage(
            HttpServletRequest request,
            @Parameter(description = "页码（从1开始）", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "20") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字符串（格式：+field1,-field2）") @RequestParam(required = false) String orders,
            @Parameter(description = "排除字段列表（格式：field1,field2）") @RequestParam(required = false) String excludes) {
        try {
            log.debug("分页查询实体列表: page={}, size={}, orders={}, excludes={}", page, size, orders, excludes);

            // 解析查询条件
            List<QueryCondition> conditions = queryConditionParser.parseConditions(request.getParameterMap());

            // 解析排序条件
            List<SortOrder> sortOrders = StringUtils.hasText(orders) ?
                    SortOrder.parseOrderString(orders) : SortOrder.defaultSort();

            // 解析排除字段
            List<String> excludeFields = parseExcludeFields(excludes);

            // 构建分页请求
            //PageRequest pageRequest = PageRequest.of(page, size, sortOrders, conditions, excludeFields);

            // 执行查询
            PageResult<T> pageResult;
            if (excludeFields.isEmpty()) {
                // 没有排除字段，使用普通查询
                pageResult = baseService.findByPageRequest(PageRequest.of(page, size, sortOrders, conditions));
            } else {
                // 有排除字段，先查询再处理
                pageResult = baseService.findByPageRequest(PageRequest.of(page, size, sortOrders, conditions));
                // 对结果进行字段排除处理
                pageResult = excludeFieldsFromPageResult(pageResult, excludeFields);
            }

            log.debug("分页查询实体列表成功: 总数={}, 当前页数据={}, 排除字段={}",
                    pageResult.getTotal(), pageResult.getNumberOfElements(), excludeFields);

            return Result.success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("分页查询实体列表异常: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有实体列表（不分页）
     *
     * @param request HTTP请求对象（用于获取查询参数）
     * @return 查询结果
     */
    @GetMapping("/all")
    @Operation(summary = "查询所有实体列表", description = "根据查询条件查询所有实体列表（不分页）")
    @ResponseBody
    public Result<List<T>> findAll(HttpServletRequest request) {
        try {
            log.debug("查询所有实体列表");

            // 解析查询条件
            List<QueryCondition> conditions = queryConditionParser.parseConditions(request.getParameterMap());

            // 执行查询
            List<T> entities = baseService.findByConditions(conditions);

            log.debug("查询所有实体列表成功: {} 条", entities.size());
            return Result.success("查询成功", entities);

        } catch (Exception e) {
            log.error("查询所有实体列表异常: {}", e.getMessage(), e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 统计实体数量
     *
     * @param request HTTP请求对象（用于获取查询参数）
     * @return 统计结果
     */
    @GetMapping("/count")
    @Operation(summary = "统计实体数量", description = "根据查询条件统计实体数量")
    @ResponseBody
    public Result<Long> count(HttpServletRequest request) {
        try {
            log.debug("统计实体数量");

            // 解析查询条件
            List<QueryCondition> conditions = queryConditionParser.parseConditions(request.getParameterMap());

            // 执行统计
            long count = baseService.countByConditions(conditions);

            log.debug("统计实体数量成功: {} 条", count);
            return Result.success("统计成功", count);

        } catch (Exception e) {
            log.error("统计实体数量异常: {}", e.getMessage(), e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }

    /**
     * 检查实体是否存在
     *
     * @param id 实体ID
     * @return 检查结果
     */
    @GetMapping("/{id}/exists")
    @Operation(summary = "检查实体是否存在", description = "根据ID检查实体是否存在")
    @ResponseBody
    public Result<Boolean> existsById(
            @Parameter(description = "实体ID", required = true) @PathVariable @NotNull ID id) {
        try {
            log.debug("检查实体是否存在: ID={}", id);

            boolean exists = baseService.existsById(id);

            log.debug("检查实体是否存在结果: ID={}, 存在={}", id, exists);
            return Result.success("检查成功", exists);

        } catch (Exception e) {
            log.error("检查实体是否存在异常: ID={}, 错误={}", id, e.getMessage(), e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    // 抽象方法，子类需要实现

    /**
     * 获取实体的ID
     * 子类需要实现此方法来获取实体的ID值
     *
     * @param entity 实体对象
     * @return 实体ID
     */
    protected abstract ID getEntityId(T entity);

    /**
     * 解析排除字段列表
     *
     * @param excludes 排除字段字符串（格式：field1,field2,field3）
     * @return 排除字段列表
     */
    private List<String> parseExcludeFields(String excludes) {
        List<String> excludeFields = new ArrayList<>();

        if (StringUtils.hasText(excludes)) {
            String[] fields = excludes.split(",");
            for (String field : fields) {
                String trimmedField = field.trim();
                if (StringUtils.hasText(trimmedField)) {
                    excludeFields.add(trimmedField);
                }
            }
        }

        return excludeFields;
    }

    /**
     * 从分页结果中排除指定字段
     *
     * @param pageResult 原始分页结果
     * @param excludeFields 要排除的字段列表
     * @return 处理后的分页结果
     */
    private PageResult<T> excludeFieldsFromPageResult(PageResult<T> pageResult, List<String> excludeFields) {
        if (pageResult == null || excludeFields == null || excludeFields.isEmpty()) {
            return pageResult;
        }

        try {
            // 对每个实体对象进行字段排除处理
            List<T> processedContent = pageResult.getContent().stream()
                    .map(entity -> excludeFieldsFromEntity(entity, excludeFields))
                    .collect(java.util.stream.Collectors.toList());

            // 创建新的分页结果
            PageResult<T> newPageResult = new PageResult<>();
            newPageResult.setContent(processedContent);
            newPageResult.setPage(pageResult.getPage());
            newPageResult.setSize(pageResult.getSize());
            newPageResult.setTotal(pageResult.getTotal());
            newPageResult.setTotalPages(pageResult.getTotalPages());
            newPageResult.setFirst(pageResult.isFirst());
            newPageResult.setLast(pageResult.isLast());
            newPageResult.setHasNext(pageResult.isHasNext());
            newPageResult.setHasPrevious(pageResult.isHasPrevious());
            newPageResult.setNumberOfElements(processedContent.size());
            newPageResult.setEmpty(processedContent.isEmpty());

            return newPageResult;
        } catch (Exception e) {
            log.error("字段排除处理失败: {}", e.getMessage(), e);
            return pageResult; // 失败时返回原结果
        }
    }

    /**
     * 从实体对象中排除指定字段
     *
     * @param entity 实体对象
     * @param excludeFields 要排除的字段列表
     * @return 处理后的实体对象
     */
    private T excludeFieldsFromEntity(T entity, List<String> excludeFields) {
        if (entity == null || excludeFields == null || excludeFields.isEmpty()) {
            return entity;
        }

        try {
            for (String fieldName : excludeFields) {
                try {
                    java.lang.reflect.Field field = findField(entity.getClass(), fieldName);
                    if (field != null) {
                        field.setAccessible(true);
                        Object oldValue = field.get(entity);
                        field.set(entity, null);
                        log.debug("排除字段: {} = {} -> null", fieldName, oldValue);
                    }
                } catch (Exception e) {
                    log.warn("无法排除字段: {}, 错误: {}", fieldName, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("实体字段排除处理失败: {}", e.getMessage(), e);
        }

        return entity;
    }

    /**
     * 查找类中的字段（包括父类）
     */
    private java.lang.reflect.Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 设置实体的ID
     * 子类需要实现此方法来设置实体的ID值
     *
     * @param entity 实体对象
     * @param id 实体ID
     */
    protected abstract void setEntityId(T entity, ID id);
}
