package com.hys.hm.shared.encrypt.service;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.util.FrontendCryptoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 脱敏响应包装器
 * 动态添加脱敏字段，无需修改原DTO类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Slf4j
public class MaskResponseWrapper<T> {

    /**
     * 原始数据对象
     */
    @JsonUnwrapped
    private final T originalData;

    /**
     * 脱敏字段映射
     */
    @JsonIgnore
    private final Map<String, Object> maskedFields = new HashMap<>();

    /**
     * 加密字段映射
     */
    @JsonIgnore
    private final Map<String, Object> encryptedFields = new HashMap<>();

    /**
     * 构造函数
     */
    public MaskResponseWrapper(T originalData) {
        this.originalData = originalData;
    }

    /**
     * 获取原始数据
     */
    @JsonIgnore
    public T getOriginalData() {
        return originalData;
    }

    /**
     * 添加脱敏字段
     */
    public void addMaskedField(String fieldName, Object maskedValue) {
        maskedFields.put(fieldName + "_masked", maskedValue);
    }

    /**
     * 添加加密字段
     */
    public void addEncryptedField(String fieldName, Object encryptedValue) {
        encryptedFields.put(fieldName + "_encrypted", encryptedValue);
    }

    /**
     * 获取所有脱敏字段（用于JSON序列化）
     */
    @JsonAnyGetter
    public Map<String, Object> getMaskedFields() {
        // 合并脱敏字段和加密字段
        Map<String, Object> allFields = new HashMap<>();
        allFields.putAll(maskedFields);
        allFields.putAll(encryptedFields);
        return allFields;
    }

    /**
     * 创建包装器并自动处理脱敏和加密
     */
    public static <T> MaskResponseWrapper<T> wrap(T data, MaskService maskService) {
        return wrap(data, maskService, null);
    }

    /**
     * 创建包装器并自动处理脱敏和加密
     */
    public static <T> MaskResponseWrapper<T> wrap(T data, MaskService maskService, FrontendCryptoUtil cryptoUtil) {
        if (data == null) {
            return null;
        }

        MaskResponseWrapper<T> wrapper = new MaskResponseWrapper<>(data);

        try {
            // 查找需要脱敏和加密的字段
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(EncryptField.class)) {
                    EncryptField encryptField = field.getAnnotation(EncryptField.class);

                    field.setAccessible(true);
                    Object value = field.get(data);

                    if (value != null && StringUtils.hasText(value.toString())) {
                        String originalValue = value.toString();

                        // 1. 处理脱敏
                        if (encryptField.hideInResult()) {
                            field.set(data, null);
                            //wrapper.addMaskedField(field.getName(), null);
                        } else {
                            // 根据脱敏级别进行脱敏
                            String maskedValue = maskService.mask(originalValue, encryptField.maskLevel());
                            field.set(data, maskedValue);
                            //wrapper.addMaskedField(field.getName(), maskedValue);
                        }

                        // 2. 处理加密（如果提供了加密工具）
                        if (cryptoUtil != null) {
                            try {
                                // 生成字段专用的密钥ID
                                String keyId = generateFieldKeyId(data.getClass(), field.getName());

                                // 根据配置的加密类型进行加密
                                FrontendCryptoUtil.EncryptedData encryptedData = null;
                                switch (encryptField.type()) {
                                    case AES:
                                        encryptedData = cryptoUtil.encryptWithAES(originalValue, keyId);
                                        break;
                                    case SM4:
                                        encryptedData = cryptoUtil.encryptWithSM4(originalValue, keyId);
                                        break;
                                    default:
                                        encryptedData = cryptoUtil.encryptWithAES(originalValue, keyId);
                                        break;
                                }

                                wrapper.addEncryptedField(field.getName(), encryptedData);

                                log.debug("字段加密: {}#{} -> [ENCRYPTED]",
                                         data.getClass().getSimpleName(), field.getName());

                            } catch (Exception encryptException) {
                                log.error("字段加密失败: {}#{}, 错误: {}",
                                         data.getClass().getSimpleName(), field.getName(),
                                         encryptException.getMessage());
                                // 加密失败时不影响脱敏功能
                            }
                        }

                        log.debug("字段处理完成: {}#{} -> [MASKED & ENCRYPTED]",
                                 data.getClass().getSimpleName(), field.getName());
                    }
                }
            }

        } catch (Exception e) {
            log.error("创建脱敏包装器失败: {}", e.getMessage(), e);
        }

        return wrapper;
    }

    /**
     * 生成字段专用的密钥ID
     */
    private static String generateFieldKeyId(Class<?> entityClass, String fieldName) {
        return String.format("%s_%s_key", entityClass.getSimpleName().toLowerCase(), fieldName);
    }
}
