package com.hys.hm.shared.encrypt.util;

import java.util.Base64;
import java.util.regex.Pattern;

/**
 * AES加密字符串检验工具类
 * 注意：此类只能进行概率性判断，不能100%确定一个字符串是否为AES加密
 */
public class AesCheckerUtil {

    // Base64编码的正则表达式
    private static final Pattern BASE64_PATTERN = Pattern.compile("^[A-Za-z0-9+/]+[=]{0,2}$");

    // 常见的AES块大小（128, 192, 256位）对应字节数
    private static final int[] AES_BLOCK_SIZES = {16, 24, 32};

    /**
     * 检查字符串是否可能是AES加密后的数据
     * @param str 待检查的字符串
     * @return 如果可能是AES加密字符串则返回true，否则返回false
     */
    public static boolean isPossibleAesEncryptedString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        // 检查是否符合Base64编码特征（AES加密结果通常会进行Base64编码）
        if (!isBase64String(str)) {
            return false;
        }

        try {
            // 解码Base64字符串
            byte[] decodedBytes = Base64.getDecoder().decode(str);

            // 检查解码后的长度是否符合AES加密特征（至少为一个块大小）
            if (decodedBytes.length < AES_BLOCK_SIZES[0]) {
                return false;
            }

            // 检查是否可能是使用了填充模式的AES加密结果
            // PKCS#7/PKCS#5填充的特点是填充的字节值等于填充的长度
            if (isPossiblePkcsPadded(decodedBytes)) {
                return true;
            }

            // 检查是否是AES块大小的倍数（对于不使用填充的情况）
            for (int blockSize : AES_BLOCK_SIZES) {
                if (decodedBytes.length % blockSize == 0) {
                    return true;
                }
            }

            return false;
        } catch (IllegalArgumentException e) {
            // Base64解码失败
            return false;
        }
    }

    /**
     * 检查字符串是否符合Base64编码格式
     * @param str 待检查的字符串
     * @return 如果是Base64字符串则返回true，否则返回false
     */
    private static boolean isBase64String(String str) {
        // 检查长度是否为4的倍数
        if (str.length() % 4 != 0) {
            return false;
        }
        // 检查是否符合Base64字符集
        return BASE64_PATTERN.matcher(str).matches();
    }

    /**
     * 检查字节数组是否可能使用了PKCS#7/PKCS#5填充
     * @param bytes 待检查的字节数组
     * @return 如果可能使用了PKCS填充则返回true，否则返回false
     */
    private static boolean isPossiblePkcsPadded(byte[] bytes) {
        if (bytes.length == 0) {
            return false;
        }

        // 最后一个字节的值表示填充长度
        int paddingLength = bytes[bytes.length - 1] & 0xFF;

        // 填充长度必须大于0且小于等于块大小
        if (paddingLength <= 0 || paddingLength > AES_BLOCK_SIZES[0]) {
            return false;
        }

        // 填充长度不能超过字节数组长度
        if (paddingLength > bytes.length) {
            return false;
        }

        // 检查所有填充字节是否都等于填充长度
        for (int i = bytes.length - paddingLength; i < bytes.length; i++) {
            if ((bytes[i] & 0xFF) != paddingLength) {
                return false;
            }
        }

        return true;
    }
}

