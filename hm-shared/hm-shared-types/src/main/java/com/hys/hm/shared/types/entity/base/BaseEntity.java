package com.hys.hm.shared.types.entity.base;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体类
 * 提供通用的实体字段和审计功能
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-29
 */
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@Data
public abstract class BaseEntity<ID extends Serializable> implements Serializable {

    /**
     * 主键ID
     * 子类需要重写此方法并添加具体的JPA注解
     */
    public abstract ID getId();

    /**
     * 设置主键ID
     * 子类需要重写此方法
     */
    public abstract void setId(ID id);

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    @Comment("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "update_time", nullable = false)
    @Comment("更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    @Column(name = "create_by", length = 32, updatable = false)
    @Comment("创建者ID")
    private String createBy;

    /**
     * 更新者ID
     */
    @Column(name = "update_by", length = 32)
    @Comment("更新者ID")
    private String updateBy;

    /**
     * 逻辑删除标记
     * 0-未删除，1-已删除
     */
    @Column(name = "deleted", nullable = false)
    @Comment("逻辑删除标记：0-未删除，1-已删除")
    private Integer deleted = 0;

    /**
     * 版本号（用于乐观锁）
     */
    @Version
    @Column(name = "version", nullable = false)
    @Comment("版本号（乐观锁）")
    private Long version = 0L;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    @Comment("备注")
    private String remark;

    /**
     * 判断实体是否为新实体（ID为空）
     */
    @Transient
    public boolean isNew() {
        return getId() == null;
    }

    /**
     * 判断实体是否已删除
     */
    @Transient
    public boolean isDeleted() {
        return deleted != null && deleted == 1;
    }

    /**
     * 标记为已删除
     */
    public void markDeleted() {
        this.deleted = 1;
    }

    /**
     * 标记为未删除
     */
    public void markUndeleted() {
        this.deleted = 0;
    }

    /**
     * 在持久化之前执行的操作
     */
    @PrePersist
    protected void prePersist() {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
        if (updateTime == null) {
            updateTime = LocalDateTime.now();
        }
        if (deleted == null) {
            deleted = 0;
        }
        if (version == null) {
            version = 0L;
        }
    }

    /**
     * 在更新之前执行的操作
     */
    @PreUpdate
    protected void preUpdate() {
        updateTime = LocalDateTime.now();
    }
}
