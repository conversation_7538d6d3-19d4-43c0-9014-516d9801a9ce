package com.hys.hm.shared.types.entity.patient;

import com.hys.hm.shared.encrypt.annotation.EncryptField;
import com.hys.hm.shared.encrypt.listener.FrameworkEncryptEntityListener;
import com.hys.hm.shared.types.entity.base.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 患者基本信息实体类
 * 对应数据库表 dp_basic_info
 *
 * 职责说明：
 * 1. 仅作为数据载体，映射数据库表结构
 * 2. 不包含任何业务逻辑
 * 3. 通过应用服务进行业务操作
 * 4. 提供基础的数据验证（如@NotNull等）
 *
 * 使用约束：
 * 1. 禁止在Entity中添加业务方法
 * 2. 禁止在Entity中进行复杂计算
 * 3. 所有业务逻辑必须在应用服务或领域服务中实现
 * 4. Entity仅用于数据持久化和传输
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Entity
@Table(name = "dp_basic_info")
@Data
@EqualsAndHashCode(callSuper = true)
@Comment("患者基本信息表")
@EntityListeners(FrameworkEncryptEntityListener.class)
public class PatientBasicInfoEntity extends BaseEntity<String> {

    @Id
    @Column(name = "ID", length = 32)
    @Comment("主键ID")
    private String id;

    @Column(name = "NAME", length = 50)
    @Comment("姓名")
    private String name;

    @Column(name = "SEX", length = 10)
    @Comment("性别")
    private String sex;

    @Column(name = "BIRTHDAY")
    @Comment("出生日期")
    private LocalDateTime birthday;

    @Column(name = "IDCARD", length = 200)  // 加密后长度会增加
    @Comment("身份证号")
    @EncryptField(type = EncryptField.EncryptType.AES,fuzzySearch = true, maskLevel = EncryptField.MaskLevel.PARTIAL,tokenLength = 4, description = "身份证号")
    private String idcard;

    @Column(name = "PHONE", length = 200)  // 加密后长度会增加
    @Comment("联系电话")
    @EncryptField(type = EncryptField.EncryptType.AES,fuzzySearch = true, maskLevel = EncryptField.MaskLevel.PARTIAL, tokenLength = 4,description = "联系电话")
    private String phone;

    @Column(name = "ADDRESS", length = 500)  // 加密后长度会增加
    @Comment("现住址")
    @EncryptField(type = EncryptField.EncryptType.AES, fuzzySearch = true, tokenLength = 2, maskLevel = EncryptField.MaskLevel.PARTIAL, description = "现住址")
    private String address;

    @Column(name = "REGISTER_ADDRESS", length = 500)  // 加密后长度会增加
    @Comment("户籍地址")
    @EncryptField(type = EncryptField.EncryptType.AES, fuzzySearch = true, tokenLength = 2, maskLevel = EncryptField.MaskLevel.PARTIAL, description = "户籍地址")
    private String registerAddress;

    @Column(name = "STREET", length = 50)
    @Comment("街道")
    private String street;

    @Column(name = "STREET_CODE", length = 40)
    @Comment("街道编码")
    private String streetCode;

    @Column(name = "COMMITTEE_CODE", length = 40)
    @Comment("居委会编码")
    private String committeeCode;

    @Column(name = "RESIDENTS_COMMITTEE", length = 100)
    @Comment("居委会")
    private String residentsCommittee;

    @Column(name = "ORG_NAME", length = 100)
    @Comment("机构名称")
    private String orgName;

    @Column(name = "ORG_ID", length = 32)
    @Comment("机构ID")
    private String orgId;

    @Column(name = "CREATE_USER_ID", length = 32)
    @Comment("创建用户ID")
    private String createUserId;

    @Column(name = "CREATE_USER_NAME", length = 50)
    @Comment("创建用户姓名")
    private String createUserName;

    @Column(name = "DUTY_DOCTOR_NAME", length = 50)
    @Comment("责任医生姓名")
    private String dutyDoctorName;

    @Column(name = "DUTY_DOCTOR", length = 32)
    @Comment("责任医生ID")
    private String dutyDoctor;

    @Column(name = "DUTY_DOCTOR_PHONE", length = 200)
    @Comment("责任医生电话")
    @EncryptField(type = EncryptField.EncryptType.AES,fuzzySearch = true, maskLevel = EncryptField.MaskLevel.PARTIAL, tokenLength = 4,description = "责任医生电话")
    private String dutyDoctorPhone;

    @Column(name = "BUILD_DATE")
    @Comment("建档日期")
    private LocalDateTime buildDate;

    @Column(name = "PAPER_ARCHIVE_NO", length = 30)
    @Comment("纸质档案号")
    private String paperArchiveNo;

    @Column(name = "COMPANY", length = 100)
    @Comment("工作单位")
    private String company;

    @Column(name = "LINKMAN", length = 30)
    @Comment("联系人")
    private String linkman;

    @Column(name = "LINKMAN_PHONE", length = 50)
    @Comment("联系人电话")
    @EncryptField(type = EncryptField.EncryptType.AES,fuzzySearch = true, maskLevel = EncryptField.MaskLevel.PARTIAL, tokenLength = 4,description = "紧急联系人电话")
    private String linkmanPhone;

    @Column(name = "RESIDENT_TYPE", length = 30)
    @Comment("常住类型")
    private String residentType;

    @Column(name = "NATION", length = 20)
    @Comment("民族")
    private String nation;

    @Column(name = "NATION_NAME", length = 50)
    @Comment("民族名称")
    private String nationName;

    @Column(name = "BLOOD_TYPE", length = 10)
    @Comment("血型")
    private String bloodType;

    @Column(name = "BLOOD_RH", length = 10)
    @Comment("RH血型")
    private String bloodRh;

    @Column(name = "EDUCATION", length = 30)
    @Comment("文化程度")
    private String education;

    @Column(name = "VOCATION", length = 30)
    @Comment("职业")
    private String vocation;

    @Column(name = "MARITAL_STATUS", length = 10)
    @Comment("婚姻状况")
    private String maritalStatus;

    @Column(name = "PAY_TYPE", length = 100)
    @Comment("医疗费用支付方式")
    private String payType;

    @Column(name = "PAY_TYPE_OTHER", length = 100)
    @Comment("其他医疗费用支付方式")
    private String payTypeOther;

    @Column(name = "ALLERGY_HISTORY", length = 100)
    @Comment("药物过敏史")
    private String allergyHistory;

    @Column(name = "OTHER_DRUG_ALLERGY_HISTORY", length = 100)
    @Comment("其他药物过敏史")
    private String otherDrugAllergyHistory;

    @Column(name = "EXPOSURE", length = 50)
    @Comment("暴露史")
    private String exposure;

    @Column(name = "DISEASE_HISTORY", columnDefinition = "TEXT")
    @Comment("疾病史")
    private String diseaseHistory;

    @Column(name = "OPERATIVE_HISTORY", columnDefinition = "TEXT")
    @Comment("手术史")
    private String operativeHistory;

    @Column(name = "TRAUMA_HISTORY", columnDefinition = "TEXT")
    @Comment("外伤史")
    private String traumaHistory;

    @Column(name = "TRANSFUSION_HISTORY", columnDefinition = "TEXT")
    @Comment("输血史")
    private String transfusionHistory;

    @Column(name = "FATHER_HISTORY", length = 100)
    @Comment("父亲病史")
    private String fatherHistory;

    @Column(name = "FATHER_HISTORY_OTHER", length = 100)
    @Comment("父亲其他病史")
    private String fatherHistoryOther;

    @Column(name = "MOTHER_HISTORY", length = 100)
    @Comment("母亲病史")
    private String motherHistory;

    @Column(name = "MOTHER_HISTORY_OTHER", length = 100)
    @Comment("母亲其他病史")
    private String motherHistoryOther;

    @Column(name = "BROTHERS_HISTORY", length = 100)
    @Comment("兄弟姐妹病史")
    private String brothersHistory;

    @Column(name = "BROTHERS_HISTORY_OTHER", length = 100)
    @Comment("兄弟姐妹其他病史")
    private String brothersHistoryOther;

    @Column(name = "CHILDREN_HISTORY", length = 100)
    @Comment("子女病史")
    private String childrenHistory;

    @Column(name = "CHILDREN_HISTORY_OTHER", length = 100)
    @Comment("子女其他病史")
    private String childrenHistoryOther;

    @Column(name = "INHERIT_HISTORY", length = 100)
    @Comment("遗传病史")
    private String inheritHistory;

    @Column(name = "INHERIT_NAME", length = 100)
    @Comment("遗传病名称")
    private String inheritName;

    @Column(name = "DISABILITY_CONDITION", length = 50)
    @Comment("残疾情况")
    private String disabilityCondition;

    @Column(name = "DISABILITY_OTHER", length = 100)
    @Comment("其他残疾情况")
    private String disabilityOther;

    @Column(name = "HEIGHT")
    @Comment("身高")
    private Double height;

    @Column(name = "WEIGHT")
    @Comment("体重")
    private Double weight;

    @Column(name = "BMI")
    @Comment("BMI指数")
    private Double bmi;

    @Column(name = "COMMUNITY", length = 100)
    @Comment("社区")
    private String community;

    @Column(name = "BUILDING", length = 100)
    @Comment("楼栋")
    private String building;

    @Column(name = "UNIT", length = 100)
    @Comment("单元")
    private String unit;

    @Column(name = "HOUSEHOLD", length = 100)
    @Comment("户号")
    private String household;

    @Column(name = "RECORD_STATUS", length = 10)
    @Comment("档案状态")
    private String recordStatus;

    @Column(name = "STOP_REASON", length = 100)
    @Comment("停止原因")
    private String stopReason;

    @Column(name = "PINYIN_NAME", length = 20)
    @Comment("拼音姓名")
    private String pinyinName;

    @Column(name = "SIGN_STATE", length = 10)
    @Comment("签约状态")
    private String signState;

    @Column(name = "CROWD_ATTRIBUTE", length = 100)
    @Comment("人群属性")
    private String crowdAttribute;

    @Column(name = "EMPHASIS_CROWD", length = 100)
    @Comment("重点人群")
    private String emphasisCrowd;

    @Column(name = "MANAGER_STATUS", length = 10)
    @Comment("管理状态")
    private String managerStatus;

    @Column(name = "MANAGER_DATE")
    @Comment("管理日期")
    private LocalDateTime managerDate;

    @Column(name = "SOURCE", length = 3)
    @Comment("数据来源")
    private String source;

    @Column(name = "CREATE_TIME")
    @Comment("创建时间")
    private LocalDateTime createTime;

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }
}
