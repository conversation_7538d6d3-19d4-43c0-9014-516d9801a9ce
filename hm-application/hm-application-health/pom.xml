<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm-application</artifactId>
        <version>3.0.0-beta.1</version>
    </parent>

    <artifactId>hm-application-health</artifactId>
    <name>hm-application-health</name>
    <description>健康管理应用服务模块</description>

    <dependencies>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-shared-types</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-domain-health</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-infrastructure-ai</artifactId>
        </dependency>
    </dependencies>

</project>
