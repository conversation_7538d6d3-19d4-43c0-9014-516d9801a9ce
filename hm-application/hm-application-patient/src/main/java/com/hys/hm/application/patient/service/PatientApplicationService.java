package com.hys.hm.application.patient.service;

import com.hys.hm.application.patient.dto.*;
import com.hys.hm.application.patient.mapper.PatientMapper;
import com.hys.hm.shared.types.entity.patient.PatientBasicInfoEntity;
import com.hys.hm.domain.patient.event.PatientCreatedEvent;
import com.hys.hm.domain.patient.event.PatientDeletedEvent;
import com.hys.hm.domain.patient.repository.PatientBasicInfoRepository;
import com.hys.hm.domain.patient.service.PatientBasicInfoService;
import com.hys.hm.shared.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 患者应用服务
 * 负责协调领域对象完成业务用例
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class PatientApplicationService {

    private final PatientBasicInfoRepository patientRepository;
    private final PatientBasicInfoService patientDomainService;
    private final ApplicationEventPublisher eventPublisher;
    private final PatientMapper patientMapper;

    /**
     * 创建患者
     */
    public PatientDTO createPatient(PatientCreateDTO createDTO) {
        log.info("创建患者: {}", createDTO.getName());

        try {
            // 1. 转换为Entity
            PatientBasicInfoEntity entity = patientMapper.toEntity(createDTO);

            // 2. 使用领域服务创建患者（包含验证和业务逻辑）
            PatientBasicInfoEntity savedEntity = patientDomainService.createPatient(entity);

            // 3. 发布领域事件
            PatientCreatedEvent event = new PatientCreatedEvent(savedEntity, getCurrentUserId());
            eventPublisher.publishEvent(event);

            // 4. 返回DTO（切面会自动脱敏）
            return patientMapper.toDTO(savedEntity);

        } catch (Exception e) {
            log.error("创建患者失败: {}", e.getMessage(), e);
            throw new BusinessException("创建患者失败: " + e.getMessage());
        }
    }

    /**
     * 更新患者信息
     */
    public PatientDTO updatePatient(String patientId, PatientUpdateDTO updateDTO) {
        log.info("更新患者信息: patientId={}", patientId);

        try {
            // 1. 获取现有患者
            PatientBasicInfoEntity entity = patientRepository.findById(patientId)
                .orElseThrow(() -> new BusinessException("患者不存在: " + patientId));

            // 2. 更新患者信息
            patientMapper.updateEntity(entity, updateDTO);

            // 3. 重新计算BMI（如果身高体重有变化）
            if (updateDTO.getHeight() != null || updateDTO.getWeight() != null) {
                entity = patientDomainService.calculateAndUpdateBmi(entity.getId());
            } else {
                entity = patientRepository.save(entity);
            }

            // 4. 返回DTO
            return patientMapper.toDTO(entity);

        } catch (Exception e) {
            log.error("更新患者信息失败: patientId={}, error={}", patientId, e.getMessage(), e);
            throw new BusinessException("更新患者信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除患者
     */
    public void deletePatient(String patientId, String deleteReason) {
        log.info("删除患者: patientId={}, reason={}", patientId, deleteReason);

        try {
            // 1. 获取患者信息
            PatientBasicInfoEntity entity = patientRepository.findById(patientId)
                .orElseThrow(() -> new BusinessException("患者不存在: " + patientId));

            // 2. 使用领域服务删除患者
            boolean deleted = patientDomainService.deletePatient(patientId, deleteReason);

            if (deleted) {
                // 3. 发布领域事件
                PatientDeletedEvent event = new PatientDeletedEvent(
                    entity,
                    false, // 硬删除
                    getCurrentUserId(),
                    deleteReason
                );
                eventPublisher.publishEvent(event);
            }

        } catch (Exception e) {
            log.error("删除患者失败: patientId={}, error={}", patientId, e.getMessage(), e);
            throw new BusinessException("删除患者失败: " + e.getMessage());
        }
    }

    /**
     * 软删除患者
     */
    public void softDeletePatient(String patientId, String deleteReason) {
        log.info("软删除患者: patientId={}, reason={}", patientId, deleteReason);

        try {
            // 1. 获取患者信息
            PatientBasicInfoEntity entity = patientRepository.findById(patientId)
                .orElseThrow(() -> new BusinessException("患者不存在: " + patientId));

            // 2. 使用领域服务软删除患者
            boolean deleted = patientDomainService.softDeletePatient(patientId, deleteReason);

            if (deleted) {
                // 3. 发布领域事件
                PatientDeletedEvent event = new PatientDeletedEvent(
                    entity,
                    true, // 软删除
                    getCurrentUserId(),
                    deleteReason
                );
                eventPublisher.publishEvent(event);
            }

        } catch (Exception e) {
            log.error("软删除患者失败: patientId={}, error={}", patientId, e.getMessage(), e);
            throw new BusinessException("软删除患者失败: " + e.getMessage());
        }
    }

    /**
     * 批量导入患者
     */
    public List<PatientDTO> batchImportPatients(List<PatientCreateDTO> createDTOList) {
        log.info("批量导入患者: count={}", createDTOList.size());

        try {
            // 1. 转换为Entity列表
            List<PatientBasicInfoEntity> entities = createDTOList.stream()
                .map(patientMapper::toEntity)
                .collect(Collectors.toList());

            // 2. 使用领域服务批量导入
            List<PatientBasicInfoEntity> savedEntities = patientDomainService.batchImportPatients(entities);

            // 3. 发布批量创建事件
            savedEntities.forEach(entity -> {
                PatientCreatedEvent event = new PatientCreatedEvent(entity, getCurrentUserId());
                eventPublisher.publishEvent(event);
            });

            // 4. 返回DTO列表
            return savedEntities.stream()
                .map(patientMapper::toDTO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("批量导入患者失败: error={}", e.getMessage(), e);
            throw new BusinessException("批量导入患者失败: " + e.getMessage());
        }
    }

    /**
     * 计算并更新BMI
     */
    public PatientDTO calculateAndUpdateBmi(String patientId) {
        log.info("计算并更新BMI: patientId={}", patientId);

        try {
            PatientBasicInfoEntity entity = patientDomainService.calculateAndUpdateBmi(patientId);
            return patientMapper.toDTO(entity);

        } catch (Exception e) {
            log.error("计算并更新BMI失败: patientId={}, error={}", patientId, e.getMessage(), e);
            throw new BusinessException("计算并更新BMI失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID
     * TODO: 从安全上下文中获取当前用户ID
     */
    private String getCurrentUserId() {
        // 这里应该从Spring Security或其他安全框架中获取当前用户ID
        // 暂时返回固定值
        return "system";
    }
}
