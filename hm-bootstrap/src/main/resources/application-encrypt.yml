# 加密配置
app:
  security:
    encrypt:
      # AES加密密钥（32字符，生产环境请使用环境变量或配置中心）
      aes-key: ${ENCRYPT_AES_KEY:hm-prod-aes-key-32-characters-long}
      # SM4加密密钥（16字符，生产环境请使用环境变量或配置中心）
      sm4-key: ${ENCRYPT_SM4_KEY:hm-prod-sm4-key-16}
      # 哈希盐值（生产环境请使用环境变量或配置中心）
      salt: ${ENCRYPT_SALT:hm-prod-hash-salt-value}

      # 加密字段配置
      fields:
        # 是否启用字段加密
        enabled: ${ENCRYPT_FIELDS_ENABLED:true}
        # 是否启用模糊查询索引
        fuzzy-search-enabled: ${ENCRYPT_FUZZY_SEARCH_ENABLED:true}
        # 索引清理任务配置
        index-cleanup:
          # 是否启用定时清理
          enabled: ${ENCRYPT_INDEX_CLEANUP_ENABLED:true}
          # 清理间隔（小时）
          interval-hours: ${ENCRYPT_INDEX_CLEANUP_INTERVAL:24}
          # 保留天数
          retention-days: ${ENCRYPT_INDEX_RETENTION_DAYS:90}

# 日志配置
logging:
  level:
    com.hys.hm.shared.encrypt.*: INFO
    org.bouncycastle.*: WARN
