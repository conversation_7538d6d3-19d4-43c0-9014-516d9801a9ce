package com.hys.hm.bootstrap;

import com.hys.hm.shared.framework.repository.BaseRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 健康管理系统启动类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-23
 */
@SpringBootApplication(scanBasePackages = {
    "com.hys.hm.bootstrap",
    "com.hys.hm.interfaces.web",
    "com.hys.hm.application",
    "com.hys.hm.domain",
    "com.hys.hm.infrastructure",
    "com.hys.hm.shared.common",
    "com.hys.hm.shared.framework.config",
    "com.hys.hm.shared.encrypt"
})

@EntityScan(basePackages = {
    "com.hys.hm.infrastructure.logging.entity",
    "com.hys.hm.domain.patient.entity", "com.hys.hm.shared.*.entity"
})
@EnableJpaRepositories(
    repositoryBaseClass = BaseRepositoryImpl.class,
    basePackages = {
        "com.hys.hm.infrastructure.logging.repository",
        "com.hys.hm.shared.*.repository",
        "com.hys.hm.domain.patient.repository"
    }
)
@ComponentScan(basePackages = {
    "com.hys.hm.bootstrap",
    "com.hys.hm.interfaces.web",
    "com.hys.hm.application",
    "com.hys.hm.domain",
    "com.hys.hm.infrastructure",
    "com.hys.hm.shared.common",
    "com.hys.hm.shared.framework.*",
    "com.hys.hm.shared.encrypt.*"
})
@EnableJpaAuditing
@EnableScheduling
@EnableConfigurationProperties
public class HealthManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(HealthManagementApplication.class, args);
    }
}
