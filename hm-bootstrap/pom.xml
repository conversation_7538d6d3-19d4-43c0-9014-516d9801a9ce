<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hys</groupId>
        <artifactId>hm</artifactId>
        <version>3.0.0-beta.1</version>
    </parent>

    <artifactId>hm-bootstrap</artifactId>
    <name>hm-bootstrap</name>
    <description>应用启动模块</description>

    <dependencies>
        <!-- 引入所有需要的模块 -->
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-interfaces-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-application-patient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-application-health</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-application-referral</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-infrastructure-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hys</groupId>
            <artifactId>hm-infrastructure-logging</artifactId>
        </dependency>

        <!-- Spring Boot 启动器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!-- 排除Lombok -->
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                    <!-- 启用实时编译 -->
                    <fork>false</fork>
                    <useTestClasspath>false</useTestClasspath>
                    <!-- 添加依赖模块的classes目录到classpath -->
                    <additionalClasspathElements>
                        <additionalClasspathElement>${project.parent.basedir}/hm-shared/hm-shared-common/target/classes</additionalClasspathElement>
                        <additionalClasspathElement>${project.parent.basedir}/hm-shared/hm-shared-types/target/classes</additionalClasspathElement>
                        <additionalClasspathElement>${project.parent.basedir}/hm-shared/hm-shared-logging/target/classes</additionalClasspathElement>
                        <additionalClasspathElement>${project.parent.basedir}/hm-interfaces/hm-interfaces-web/target/classes</additionalClasspathElement>
                        <additionalClasspathElement>${project.parent.basedir}/hm-application/hm-application-patient/target/classes</additionalClasspathElement>
                        <additionalClasspathElement>${project.parent.basedir}/hm-application/hm-application-health/target/classes</additionalClasspathElement>
                        <additionalClasspathElement>${project.parent.basedir}/hm-infrastructure/hm-infrastructure-ai/target/classes</additionalClasspathElement>
                        <additionalClasspathElement>${project.parent.basedir}/hm-infrastructure/hm-infrastructure-logging/target/classes</additionalClasspathElement>
                    </additionalClasspathElements>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
