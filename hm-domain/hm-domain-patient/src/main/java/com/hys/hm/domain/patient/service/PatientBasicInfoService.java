package com.hys.hm.domain.patient.service;

import com.hys.hm.shared.types.entity.patient.PatientBasicInfoEntity;
import com.hys.hm.shared.common.page.PageRequest;
import com.hys.hm.shared.common.page.PageResult;
import com.hys.hm.shared.framework.service.BaseService;

import java.util.List;
import java.util.Optional;

/**
 * 患者基本信息服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
public interface PatientBasicInfoService extends BaseService<PatientBasicInfoEntity, String> {

    /**
     * 根据身份证号查找患者基本信息
     */
    Optional<PatientBasicInfoEntity> findByIdcard(String idcard);

    /**
     * 根据手机号查找患者基本信息
     */
    Optional<PatientBasicInfoEntity> findByPhone(String phone);

    /**
     * 根据姓名模糊查询患者基本信息
     */
    List<PatientBasicInfoEntity> findByNameContaining(String name);

    /**
     * 根据机构ID查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByOrgId(String orgId);

    /**
     * 根据责任医生查找患者基本信息列表
     */
    List<PatientBasicInfoEntity> findByDutyDoctor(String dutyDoctor);

    /**
     * 检查身份证号是否已存在
     */
    boolean existsByIdcard(String idcard);

    /**
     * 检查手机号是否已存在
     */
    boolean existsByPhone(String phone);

    /**
     * 创建患者基本信息（带验证和事件发布）
     */
    PatientBasicInfoEntity createPatient(PatientBasicInfoEntity patient);

    /**
     * 删除患者基本信息（带事件发布）
     */
    boolean deletePatient(String patientId, String deleteReason);

    /**
     * 软删除患者基本信息（带事件发布）
     */
    boolean softDeletePatient(String patientId, String deleteReason);

    /**
     * 批量导入患者基本信息
     */
    List<PatientBasicInfoEntity> batchImportPatients(List<PatientBasicInfoEntity> patients);

    /**
     * 计算并更新BMI
     */
    PatientBasicInfoEntity calculateAndUpdateBmi(String patientId);

    /**
     * 生成拼音姓名
     */
    String generatePinyinName(String name);

    /**
     * 验证患者基本信息数据
     */
    void validatePatient(PatientBasicInfoEntity patient);

    /**
     * 根据多个条件分页查询患者基本信息
     */
    PageResult<PatientBasicInfoEntity> findByMultipleConditionsWithPage(
            String name, String idcard, String phone, String orgId,
            String dutyDoctor, String recordStatus, String signState,
            PageRequest pageRequest
    );

    /**
     * 根据年龄范围查询患者基本信息
     */
    List<PatientBasicInfoEntity> findByAgeRange(int minAge, int maxAge);

    /**
     * 根据BMI范围查询患者基本信息
     */
    List<PatientBasicInfoEntity> findByBmiRange(double minBmi, double maxBmi);

    /**
     * 查询有过敏史的患者基本信息
     */
    List<PatientBasicInfoEntity> findPatientsWithAllergyHistory();

    /**
     * 查询有家族病史的患者基本信息
     */
    List<PatientBasicInfoEntity> findPatientsWithFamilyHistory();

    /**
     * 获取患者基本信息统计
     */
    PatientStatistics getStatistics();

    /**
     * 患者统计信息
     */
    interface PatientStatistics {
        long getTotalCount();
        long getActiveCount();
        long getSignedCount();
        long getMaleCount();
        long getFemaleCount();
        double getAverageAge();
        double getAverageBmi();
        long getWithAllergyCount();
        long getWithFamilyHistoryCount();
    }
}
