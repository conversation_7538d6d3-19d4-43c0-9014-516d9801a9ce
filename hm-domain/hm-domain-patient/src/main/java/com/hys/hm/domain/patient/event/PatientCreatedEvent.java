package com.hys.hm.domain.patient.event;

import com.hys.hm.shared.types.entity.patient.PatientBasicInfoEntity;
import com.hys.hm.shared.framework.event.EntityCreatedEvent;
import lombok.Getter;

/**
 * 患者创建事件
 * 当患者基本信息被创建时发布此事件
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-30
 */
@Getter
public class PatientCreatedEvent extends EntityCreatedEvent<PatientBasicInfoEntity, String> {

    /**
     * 患者姓名
     */
    private final String patientName;

    /**
     * 患者身份证号
     */
    private final String patientIdcard;

    /**
     * 患者手机号
     */
    private final String patientPhone;

    /**
     * 责任医生ID
     */
    private final String dutyDoctorId;

    /**
     * 责任医生姓名
     */
    private final String dutyDoctorName;

    /**
     * 机构ID
     */
    private final String orgId;

    /**
     * 机构名称
     */
    private final String orgName;

    /**
     * 构造函数
     */
    public PatientCreatedEvent(PatientBasicInfoEntity patient, String operatorId) {
        super(patient, patient.getId(), PatientBasicInfoEntity.class, operatorId);
        this.patientName = patient.getName();
        this.patientIdcard = patient.getIdcard();
        this.patientPhone = patient.getPhone();
        this.dutyDoctorId = patient.getDutyDoctor();
        this.dutyDoctorName = patient.getDutyDoctorName();
        this.orgId = patient.getOrgId();
        this.orgName = patient.getOrgName();
    }

    /**
     * 静态工厂方法
     */
    public static PatientCreatedEvent of(PatientBasicInfoEntity patient) {
        return new PatientCreatedEvent(patient, "system");
    }

    /**
     * 静态工厂方法（带操作用户）
     */
    public static PatientCreatedEvent of(PatientBasicInfoEntity patient, String operatorId) {
        return new PatientCreatedEvent(patient, operatorId);
    }

    @Override
    public String toString() {
        return String.format("PatientCreatedEvent{patientId='%s', patientName='%s', patientIdcard='%s', " +
                "dutyDoctorName='%s', orgName='%s', operatorId='%s', occurredOn=%s}",
                getAggregateId(), patientName, patientIdcard, dutyDoctorName, orgName,
                getOperatorId(), getOccurredOn());
    }
}
